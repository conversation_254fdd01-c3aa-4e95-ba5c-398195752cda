import express, { Express, Request, Response } from "express";
import axios from "axios";
import { decodeAccess } from "../../../middleware/decodeAccess.mw";
import multer from "multer";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";

export class HeyGenAvatarController {
    private router = express.Router();
    private readonly HEYGEN_API_KEY = "************************************************************";

    constructor() {
        // Create custom avatar endpoint
        this.router.post(
            "/create-custom",
            [
                multer().single('image'),
                decodeAccess
            ],
            (request: Request, response: Response) => {
                return this.createCustomAvatar(request, response);
            }
        );

        // Get HeyGen voices
        this.router.get(
            "/voices",
            [decodeAccess],
            (request: Request, response: Response) => {
                return this.getVoices(request, response);
            }
        );

        // Get user's custom avatars
        this.router.get(
            "/my-avatars",
            [decodeAccess],
            (request: Request, response: Response) => {
                return this.getMyAvatars(request, response);
            }
        );

        // Generate video
        this.router.post(
            "/generate-video",
            [
                express.json({ limit: "2MB" }),
                decodeAccess
            ],
            (request: Request, response: Response) => {
                return this.generateVideo(request, response);
            }
        );

        // Get video status
        this.router.get(
            "/video-status/:videoId",
            [decodeAccess],
            (request: Request, response: Response) => {
                return this.getVideoStatus(request, response);
            }
        );
    }

    public use(expressServer: Express, path: string): void {
        expressServer.use(path, this.router);
    }

    private async createCustomAvatar(request: Request, response: Response): Promise<Response> {
        try {
            if (!request.file) {
                return response.status(400).json({
                    error: "No image file provided"
                });
            }

            const accountId = request.accountToken?.account?._id;

            if (!accountId) {
                throw new APIError(
                    APIErrorName.E_MISSING_AUTHORIZATION,
                    "Missing accountId in account token"
                );
            }

            const HEYGEN_API_KEY = "************************************************************";
            const uploadedImage = request.file;

            // Step 1: Upload user's image to HeyGen's Asset Upload API to get image_key
            const fileBuffer = uploadedImage.buffer;

            const uploadResponse = await axios.post('https://upload.heygen.com/v1/asset', fileBuffer, {
                headers: {
                    'X-Api-Key': HEYGEN_API_KEY,
                    'Content-Type': uploadedImage.mimetype
                }
            });

            if (!uploadResponse.data?.data?.id) {
                return response.status(500).json({
                    error: "Failed to upload image to HeyGen"
                });
            }

            const imageKey = uploadResponse.data.data.image_key;

            console.log("Step 1: Upload user's image to HeyGen's Asset Upload API to get image_key");
            console.log(imageKey);

            // Step 1.5: Poll asset upload status until completed
            // const pollAssetStatus = async (): Promise<string> => {
            //     const maxAttempts = 30; // 5 minutes with 10-second intervals
            //     let attempts = 0;

            //     return new Promise((resolve, reject) => {
            //         const checkStatus = async () => {
            //             try {
            //                 const statusResponse = await axios.get(`https://upload.heygen.com/v1/asset/${assetId}`, {
            //                     headers: {
            //                         'X-Api-Key': HEYGEN_API_KEY
            //                     }
            //                 });

            //                 const { status, image_key } = statusResponse.data.data;

            //                 if (status === "completed" && image_key) {
            //                     resolve(image_key);
            //                 } else if (status === "failed") {
            //                     reject(new Error("Asset upload failed"));
            //                 } else if (status === "pending" || status === "processing") {
            //                     attempts++;
            //                     if (attempts < maxAttempts) {
            //                         setTimeout(checkStatus, 10000); // Check every 10 seconds
            //                     } else {
            //                         reject(new Error("Asset upload timed out"));
            //                     }
            //                 }
            //             } catch (err) {
            //                 reject(err);
            //             }
            //         };

            //         checkStatus();
            //     });
            // };

            //const imageKey = await pollAssetStatus();

            // Check existing avatar groups
            const listGroupsResponse = await axios.get('https://api.heygen.com/v2/avatar_group.list', {
                headers: {
                    'X-Api-Key': HEYGEN_API_KEY,
                    'Accept': 'application/json'
                }
            });

            if (!listGroupsResponse.data?.data) {
                return response.status(500).json({
                    success: false,
                    error: "Failed to fetch avatar groups"
                });
            }

            const existingGroups = listGroupsResponse.data.data.avatar_group_list || [];
            const userGroup = existingGroups.find((group: any) => group.name === accountId.toString());

            let groupId: string;

            if (userGroup) {
                // User group exists, add image to existing group
                groupId = userGroup.id;

                console.log("groupId");
                console.log(groupId);

                console.log(`Adding image to existing group for user ${groupId}`);
                const addToGroupResponse = await axios.post('https://api.heygen.com/v2/photo_avatar/avatar_group/add', {
                    group_id: groupId,
                    image_keys: [imageKey],
                    name: "test name"
                }, {
                    headers: {
                        'X-Api-Key': HEYGEN_API_KEY,
                        'Content-Type': 'application/json'
                    }
                });

                console.log("Add to group response:", addToGroupResponse.data);

                if (!addToGroupResponse.data?.data) {
                    return response.status(500).json({
                        success: false,
                        error: "Failed to add image to existing avatar group"
                    });
                }
            } else {

                //Step 2: Create new group with user ID as name
                console.log(`Step 2: Creating new group for accountId ${accountId}`);
    
                const createGroupResponse = await axios.post('https://api.heygen.com/v2/photo_avatar/avatar_group/create', {
                    name: accountId.toString(),
                    image_key: imageKey
                }, {
                    headers: {
                        'X-Api-Key': HEYGEN_API_KEY,
                        'Content-Type': 'application/json'
                    }
                });

                console.log("Create group response:", createGroupResponse.data);

                if (!createGroupResponse.data?.data?.group_id) {
                    return response.status(500).json({
                        success: false,
                        error: "Failed to create avatar group"
                    });
                }

                    groupId = createGroupResponse.data.data.group_id;
    



                // Step 3: Wait for avatar group to be ready
                const pollGroupStatus = async (): Promise<void> => {
                    const maxAttempts = 60; // 10 minutes with 10-second intervals
                    let attempts = 0;

                    return new Promise((resolve, reject) => {
                        const checkStatus = async () => {
                            try {
                                const statusResponse = await axios.get(`https://api.heygen.com/v2/avatar_group//${groupId}/avatars`, {
                                    headers: {
                                        'X-Api-Key': HEYGEN_API_KEY
                                    }
                                });

                                console.log("Step 3: Wait for avatar group to be ready");
                                console.log(statusResponse.data);

                                if (statusResponse.data?.data?.avatar_list?.length > 0) {
                                if (statusResponse.data.data.avatar_list[0].status === "completed") {
                                    resolve();
                                } else {
                                    attempts++;
                                    if (attempts < maxAttempts) {
                                        setTimeout(checkStatus, 10000); // Check every 10 seconds
                                    } else {
                                        reject(new Error("Avatar group creation timed out"));
                                    }
                                }
                                } else {
                                    attempts++;
                                    if (attempts < maxAttempts) {
                                        setTimeout(checkStatus, 10000); // Check every 10 seconds
                                    } else {
                                        reject(new Error("Avatar group creation timed out"));
                                    }
                                }

                            } catch (err) {
                                console.log("pollGroupStatus error:", err);
                                reject(err);
                            }
                        };

                        checkStatus();
                    });
                }

                await pollGroupStatus();
            }

            // // Step 5: Train the avatar group
            // const trainResponse = await axios.post('https://api.heygen.com/v2/photo_avatar/train', {
            //     group_id: groupId
            // }, {
            //     headers: {
            //         'X-Api-Key': HEYGEN_API_KEY,
            //         'Content-Type': 'application/json'
            //     }
            // });

            // console.log("Step 5: Train the avatar group");
            // console.log(trainResponse.data);

            // if (trainResponse.status !== 200) {
            //     return response.status(500).json({
            //         error: "Failed to start avatar training"
            //     });
            // }

            // // Step 6: Poll for training completion
            // const pollTrainingStatus = async (): Promise<void> => {
            //     const maxAttempts = 60; // 10 minutes with 10-second intervals
            //     let attempts = 0;

            //     return new Promise((resolve, reject) => {
            //         const checkStatus = async () => {
            //             try {
            //                 const statusResponse = await axios.get(`https://api.heygen.com/v2/photo_avatar/train/status/${groupId}`, {
            //                     headers: {
            //                         'X-Api-Key': HEYGEN_API_KEY
            //                     }
            //                 });

            //                 const { status } = statusResponse.data.data;

            //                 console.log("Step 6: Poll for training completion");
            //                 console.log(statusResponse.data);

            //                 if (status === "completed") {
            //                     resolve();
            //                 } else if (status === "failed") {
            //                     reject(new Error("Avatar training failed"));
            //                 } else if (status === "processing" || status === "pending") {
            //                     attempts++;
            //                     if (attempts < maxAttempts) {
            //                         setTimeout(checkStatus, 10000); // Check every 10 seconds
            //                     } else {
            //                         reject(new Error("Avatar training timed out"));
            //                     }
            //                 }
            //             } catch (err) {
            //                 reject(err);
            //             }
            //         };

            //         checkStatus();
            //     });
            // };

            // await pollTrainingStatus();

            // console.log("Step 6: Poll for training completion");

            // // Step 7: Get avatar list from the group
            // const avatarListResponse = await axios.get(`https://api.heygen.com/v2/avatar_group/${groupId}/avatars`, {
            //     headers: {
            //         'X-Api-Key': HEYGEN_API_KEY
            //     }
            // });

            // if (!avatarListResponse.data?.data?.avatar_list?.length) {
            //     return response.status(500).json({
            //         error: "Failed to get trained avatars"
            //     });
            // }

            // console.log("Step 7: Get avatar list from the group");
            // console.log(avatarListResponse.data);

            // // Use the first avatar from the list
            // const avatarId = avatarListResponse.data.data.avatar_list[0].id;



            return response.status(200).json({
                success: true,
                data: {
                    group_id: groupId,
                    message: "Custom avatar created successfully"
                }
            });
 


        } catch (error: any) {
            //console.error("Custom avatar creation error:", error);
            console.log("Custom avatar creation error:", error?.response?.data?.error);
            return response.status(500).json({
                error: "Failed to create custom avatar: " + (error instanceof Error ? error.message : "Unknown error")
            });
        }
    }


    // Get HeyGen voices
    private async getVoices(request: Request, response: Response): Promise<Response> {
        try {
            const voicesResponse = await axios.get('https://api.heygen.com/v2/voices', {
                headers: {
                    'Accept': 'application/json',
                    'X-Api-Key': this.HEYGEN_API_KEY
                }
            });

            return response.json({
                success: true,
                data: voicesResponse.data.data
            });
        } catch (error: any) {
            console.error("Error fetching HeyGen voices:", error?.response?.data || error.message);
            return response.status(500).json({
                success: false,
                error: "Failed to fetch voices"
            });
        }
    }

    // Get user's custom avatars
    private async getMyAvatars(request: Request, response: Response): Promise<Response> {
        try {

            const accountId = request.accountToken?.account?._id;
            if (!accountId) {
                return response.status(401).json({
                    success: false,
                    error: "account ID not found in token"
                });
            }

            // Step 1: Get all avatar groups
            const groupsResponse = await axios.get('https://api.heygen.com/v2/avatar_group.list', {
                headers: {
                    'X-Api-Key': this.HEYGEN_API_KEY,
                    'Accept': 'application/json'
                }
            });

            if (!groupsResponse.data?.data) {
                return response.status(500).json({
                    success: false,
                    error: "Failed to fetch avatar groups"
                });
            }

            console.log(groupsResponse.data.data);

            const existingGroups = groupsResponse.data.data.avatar_group_list || [];
            const userGroups = existingGroups.filter((group: any) => group.name === accountId.toString());

            if (userGroups.length === 0) {
                return response.json({
                    success: true,
                    data: {
                        avatar_groups: [],
                        avatar_list: []
                    }
                });
            }

            // Step 2: Get avatars for each user group
            const allAvatars: any[] = [];

            for (const group of userGroups) {
                try {
                    const avatarsResponse = await axios.get(`https://api.heygen.com/v2/avatar_group/${group.id}/avatars`, {
                        headers: {
                            'Accept': 'application/json',
                            'X-Api-Key': this.HEYGEN_API_KEY
                        }
                    });

                    const avatarsData = avatarsResponse.data.data;
                    if (avatarsData.avatar_list && avatarsData.avatar_list.length > 0) {
                        // Add group_id to each avatar for reference
                        const avatarsWithGroupId = avatarsData.avatar_list.map((avatar: any) => ({
                            ...avatar,
                            group_id: group.id
                        }));
                        allAvatars.push(...avatarsWithGroupId);
                    }
                } catch (groupError) {
                    console.warn(`Failed to fetch avatars for group ${group.id}:`, groupError);
                    // Continue with other groups even if one fails
                }
            }

            return response.json({
                success: true,
                data: {
                    avatar_groups: userGroups,
                    avatar_list: allAvatars
                }
            });
        } catch (error: any) {
            console.error("Error fetching user's avatars:", error?.response?.data || error.message);
            return response.status(500).json({
                success: false,
                error: "Failed to fetch user avatars"
            });
        }
    }

    // Generate video
    private async generateVideo(request: Request, response: Response): Promise<Response> {
        try {
            const videoRequest = request.body;

            if (!videoRequest) {
                return response.status(400).json({
                    success: false,
                    error: "Video generation request body is required"
                });
            }

            const generateResponse = await axios.post('https://api.heygen.com/v2/video/generate', videoRequest, {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Api-Key': this.HEYGEN_API_KEY
                }
            });

            return response.json({
                success: true,
                data: generateResponse.data
            });
        } catch (error: any) {
            console.error("Error generating video:", error?.response?.data || error.message);
            return response.status(500).json({
                success: false,
                error: "Failed to generate video"
            });
        }
    }

    // Get video status
    private async getVideoStatus(request: Request, response: Response): Promise<Response> {
        try {
            const { videoId } = request.params;

            if (!videoId) {
                return response.status(400).json({
                    success: false,
                    error: "Video ID is required"
                });
            }

            const statusResponse = await axios.get(`https://api.heygen.com/v1/video_status.get?video_id=${videoId}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Api-Key': this.HEYGEN_API_KEY
                }
            });

            return response.json({
                success: true,
                data: statusResponse.data
            });
        } catch (error: any) {
            console.error("Error fetching video status:", error?.response?.data || error.message);
            return response.status(500).json({
                success: false,
                error: "Failed to fetch video status"
            });
        }
    }
}
