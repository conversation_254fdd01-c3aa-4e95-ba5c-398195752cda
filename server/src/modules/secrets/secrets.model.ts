import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
import * as fs from "fs";
import * as util from "util";
import { APIErrorName } from "../../interfaces/apiTypes";
import {
	getLocalPath,
	LocalPathType
} from "../../utils/helpers/gp.helper";
import {
	gpLog,
	LogScope
} from "../../utils/managers/gpLog.manager";

const readFile = util.promisify(fs.readFile);

export interface StripeSecrets {
	privateKey: string;
	webhookSecret: string;
	retryAttempts: number;
}

export interface OIDCConfig{
	provider: string;
	clientId: string;
	clientSecret: string;
	userInfoEndpoint: string;
	jwksEndpoint: string;
	tokenEndpoint: string;
	redirectEndpoint: string;
}

export interface SpeechToTextSecrets {
	bucketName: string;
	apiEndpoint: string;
	storageUrl: string;
	credentials: {
		client_email: string;
		private_key: string;
		project_id: string;
	};
}

export interface VertexSecrets {
	credentials: {
		type: string;
		project_id: string;
		private_key_id: string;
		private_key: string;
		client_email: string;
		client_id: string;
		auth_uri: string;
		token_uri: string;
		auth_provider_x509_cert_url: string;
		client_x509_cert_url: string;
		universe_domain: string;
	};
}

export interface ISecrets {
	mongo: {
		mongoProtocol: string;
		mongoHost: string;
		mongoUsername: string;
		mongoPassword: string;
		dbName: string;
	};
	sendGrid: {
		fromAddress: string;
		host: string;
		apiKey: string;
	};
	hashkey: {
		key: string;
	};
	storage: {
		bucketName: string;
		tempBucketName: string;
		host: string;
		isLocal: boolean;
	};
	cdn: {
		host: string;
	};
	player: {
		host: string;
	};
	iframely: {
		host: string;
		apiKey: string;
	};
	stripe: StripeSecrets;
	cron: {
		privateKey: string;
	},
	elevenlabs: {
		apiKey: string;
	};
	oidc: OIDCConfig[];
	speechToText: SpeechToTextSecrets;
	vertex: VertexSecrets;
}

let secrets: ISecrets | undefined = undefined;

const readSecretFromJson = async (): Promise<ISecrets | undefined> => {
	try {
		const localSecretsPath = getLocalPath(LocalPathType.SECRETS);
		if (!localSecretsPath) {
			return undefined;
		}
		const secretPath = localSecretsPath + "secrets.json";

		const fileData = await readFile(secretPath, "utf8");
		if (fileData) {
			return JSON.parse(fileData);
		}
		return undefined;

	} catch (error: any) {
		gpLog({
			message: "unable to read secrets from json",
			objData: { error: error?.message },
			trace: "utils.secretManager.readSecretFromJson",
			scope: LogScope.ERROR
		});
		return undefined;
	}
};

export const getSecrets = async (): Promise<ISecrets> => {
	try {
		if (secrets) {
			return secrets;
		}

		const gpDevSecret = await readSecretFromJson();
		if (gpDevSecret) {
			// DEV MODE
			secrets = {
				mongo: gpDevSecret.mongo ?? undefined,
				sendGrid: gpDevSecret.sendGrid ?? undefined,
				hashkey: gpDevSecret.hashkey ?? undefined,
				storage: gpDevSecret.storage ?? undefined,
				cdn: gpDevSecret.cdn ?? undefined,
				player: gpDevSecret.player ?? undefined,
				iframely: gpDevSecret.iframely ?? undefined,
				stripe: gpDevSecret.stripe ?? undefined,
				cron: gpDevSecret.cron ?? undefined,
				oidc: gpDevSecret.oidc ?? undefined,
				speechToText: gpDevSecret.speechToText ?? undefined,
				vertex: gpDevSecret.vertex ?? undefined
			};
			gpLog({
				message: "Development secrets have been set.",
				scope: LogScope.INFO
			});
		} else {
			const secretsClient = new SecretManagerServiceClient();
			const [secret] = await secretsClient.accessSecretVersion({
				name: process.env.SECRETS_RESOURCE
			});
			const gcpSecrets = JSON.parse(secret.payload?.data?.toString() ?? "");
			secrets = {
				mongo: gcpSecrets.mongo ?? undefined,
				sendGrid: gcpSecrets.sendGrid ?? undefined,
				hashkey: gcpSecrets.hashkey ?? undefined,
				storage: gcpSecrets.storage ?? undefined,
				cdn: gcpSecrets.cdn ?? undefined,
				player: gcpSecrets.player ?? undefined,
				iframely: gcpSecrets.iframely ?? undefined,
				stripe: gcpSecrets.stripe ?? undefined,
				cron: gcpSecrets.cron ?? undefined,
				elevenlabs: gcpSecrets.elevenlabs ?? undefined,
				oidc: gcpSecrets.oidc ?? undefined,
				speechToText: gcpSecrets.speechToText ?? undefined,
				vertex: gcpSecrets.vertex ?? undefined
			};
			gpLog({
				message: "Production secrets have been set.",
				scope: LogScope.INFO
			});
		}

		validateSecrets(secrets);

		return secrets;
	} catch (error: any) {
		const err = new Error();
		err.message = `<getSecrets.service> ${APIErrorName.E_SERVICE_FAILED} | ${error.message}`;
		err.name = APIErrorName.E_SERVICE_FAILED;
		throw err;
	}
};


const validateSecrets = (secretsObj: ISecrets): void => {
	if (!secretsObj.hashkey) {
		throw new Error("missing hashkey in secrets");
	}

	if (!secretsObj.mongo) {
		throw new Error("missing mongo in secrets");
	}

	if (!secretsObj.sendGrid) {
		throw new Error("missing sendGrid in secrets");
	}

	if (!secretsObj.storage) {
		throw new Error("missing storage in secrets");
	}

	if (!secretsObj.cdn) {
		throw new Error("missing cdn in secrets");
	}

	if (!secretsObj.player) {
		throw new Error("missing player in secrets");
	}

	if (!secretsObj.iframely) {
		throw new Error("missing iframely in secrets");
	}

	if (!secretsObj.stripe) {
		throw new Error("missing stripe in secrets");
	}

	if (!secretsObj.cron) {
		throw new Error("missing cron in secrets");
	}

	if (!secretsObj.oidc) {
		throw new Error("missing oidc in secrets");
	}

	if (!secretsObj.speechToText) {
		throw new Error("missing speechToText in secrets");
	}

	if (!secretsObj.vertex) {
		throw new Error("missing vertex in secrets");
	}
};
