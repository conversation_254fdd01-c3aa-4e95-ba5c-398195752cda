import axios from "axios";
import { HeyGenVideoStatus } from "@src/types/heygen";

const getHeyGenVideoStatus = async (
    videoId: string
): Promise<{ data: HeyGenVideoStatus | null; error: any }> => {
    try {
        const API_ENDPOINT = process.env.API_ENDPOINT;
        const API_VERSION = process.env.API_VERSION;
        const accessToken = localStorage.getItem("accessToken");
        const accountToken = sessionStorage.getItem("token");

        const response = await axios.request({
            url: `${API_ENDPOINT}/api/heygen/avatars/video-status/${videoId}`,
            method: "GET",
            headers: {
                "Authorization": "Bearer " + accessToken,
                "x-api-version": API_VERSION,
                "x-account-token": accountToken,
                "Accept": "application/json"
            }
        });

        return { data: response.data, error: null };
    } catch (error) {
        console.error("Error fetching HeyGen video status:", error);
        return { data: null, error };
    }
};

export default getHeyGenVideoStatus;
