import axios from "axios";
import { HeyGenVideoGenerationRequest, HeyGenVideoGenerationResponse } from "@src/types/heygen";

const generateHeyGenVideo = async (
	request: HeyGenVideoGenerationRequest
): Promise<{ data: HeyGenVideoGenerationResponse | null; error: any }> => {
	try {
		const API_ENDPOINT = process.env.API_ENDPOINT;
		const API_VERSION = process.env.API_VERSION;
		const accessToken = localStorage.getItem("accessToken");
		const accountToken = sessionStorage.getItem("token");

		const response = await axios.request({
			url: `${API_ENDPOINT}/api/heygen/avatars/generate-video`,
			method: "POST",
			data: request,
			headers: {
				"Authorization": "Bearer " + accessToken,
				"x-api-version": API_VERSION,
				"x-account-token": accountToken,
				"Accept": "application/json",
				"Content-Type": "application/json"
			}
		});

		return { data: response.data, error: null };
	} catch (error) {
		console.error("Error generating HeyGen video:", error);
		return { data: null, error };
	}
};

export default generateHeyGenVideo;
