import axios from "axios";

interface HeyGenVoice {
    voice_id: string;
    name: string;
    language: string;
    gender: string;
    preview_audio: string;
}

const getHeyGenVoices = async (): Promise<{ data: { voices: HeyGenVoice[] } | null; error: any }> => {
    try {
        const API_ENDPOINT = process.env.API_ENDPOINT;
        const API_VERSION = process.env.API_VERSION;
        const accessToken = localStorage.getItem("accessToken");
        const accountToken = sessionStorage.getItem("token");

        const response = await axios.request({
            url: `${API_ENDPOINT}/api/heygen/avatars/voices`,
            method: "GET",
            headers: {
                "Authorization": "Bearer " + accessToken,
                "x-api-version": API_VERSION,
                "x-account-token": accountToken,
                "Accept": "application/json"
            }
        });

        return { data: response.data.data, error: null };
    } catch (error) {
        console.error("Error fetching HeyGen voices:", error);
        return { data: null, error };
    }
};

export default getHeyGenVoices;