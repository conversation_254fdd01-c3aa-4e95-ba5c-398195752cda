import axios from "axios";

interface CreateCustomAvatarResponse {
    success: boolean;
    data: {
        avatar_id: string;
        group_id: string;
        message: string;
    };
}

const createCustomHeyGenAvatar = async (imageFile: File): Promise<{ data: CreateCustomAvatarResponse | null; error: any }> => {
    try {
        const API_ENDPOINT = process.env.API_ENDPOINT;
        const API_VERSION = process.env.API_VERSION;
        const accessToken = localStorage.getItem("accessToken");
        const accountToken = sessionStorage.getItem("token");

        const formData = new FormData();
        formData.append('image', imageFile);

        const response = await axios.request({
            url: `${API_ENDPOINT}/api/heygen/avatars/create-custom`,
            method: "POST",
            data: formData,
            headers: {
                "Authorization": "Bearer " + accessToken,
                "x-api-version": API_VERSION,
                "x-account-token": accountToken,
                "Content-Type": "multipart/form-data"
            }
        });

        return { data: response.data, error: null };
    } catch (error) {
        console.error("Error creating custom HeyGen avatar:", error);
        return { data: null, error };
    }
};

export default createCustomHeyGenAvatar;
