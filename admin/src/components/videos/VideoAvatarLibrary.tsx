/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import { useTranslation } from "../hooks/translations";
import { useTokenCheck } from "../hooks/useTokenCheck";
import { useNavigate } from "react-router-dom";
import { useDropzone } from "react-dropzone";
import { getErrorString } from "../hooks/getErrorString";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import Lottie from "lottie-react";
import { LoadingSpinnerAnimation } from "@src/assets/lottie_animations/loading-spinner";
import { HeyGenAvatar, HeyGenVoice, MyHeyGenAvatar } from "@src/types/heygen";
import getHeyGenVoices from "../hooks/getHeyGenVoices";
import getMyHeyGenAvatars from "../hooks/getMyHeyGenAvatars";
import createCustomHeyGenAvatar from "../hooks/createCustomHeyGenAvatar";
import generateHeyGenVideo from "../hooks/generateHeyGenVideo";
import getHeyGenVideoStatus from "../hooks/getHeyGenVideoStatus";
import uploadVideoURL from "../hooks/uploadVideoURL";
import styled from "styled-components";
import { DisplayFormatOptions } from "@src/types/snippetOptions";
import {
	FlexSwitchRow,
	FlexSwitchCol,
	PortraitBox,
	LandscapeBox
} from "@src/styles/components";

interface Props {
    saveChanges: boolean;
    setSaveChanges: (value: boolean) => void;
    navigationUrl: string;
    showConfirmLeavingModal: boolean;
    setShowConfirmLeavingModal: (value: boolean) => void;
    hideCreateVideo: boolean;
}

// Modern styled components
const Container = styled.div`
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
`;

const Header = styled.div`
    text-align: center;
    margin-bottom: 3rem;
`;

const Title = styled.h1`
    font-size: 2.5rem;
    font-weight: 700;
    color: ${(props) => props.theme.colors.apTextColor};
    margin-bottom: 1rem;
    background: linear-gradient(135deg, ${(props) => props.theme.colors.apButton}, ${(props) => props.theme.colors.apButtonHover});
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
`;

const Subtitle = styled.p`
    font-size: 1.2rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
`;

const Section = styled.div`
    margin-bottom: 4rem;
`;

const SectionHeader = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
`;

const SectionTitle = styled.h2`
    font-size: 1.8rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin: 0;
`;

const SectionDescription = styled.p`
    font-size: 1rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    margin: 0.5rem 0 0 0;
`;

const AvatarGrid = styled.div`
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(255px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
`;

const AvatarCard = styled.div<{ selected?: boolean }>`
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 3px solid ${(props) => props.selected ? props.theme.colors.apButton : 'transparent'};
    position: relative;
		max-height: 300px;
		text-align: center;

    &:hover {
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    }

    ${(props) => props.selected && `
        &::before {
            content: '✓';
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: ${props.theme.colors.apButton};
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            z-index: 2;
        }
    `}
`;

const AvatarImage = styled.img`
    width: 100%;
    height: 100%;
    object-fit: cover;
`;

const AvatarInfo = styled.div`
    padding: 1.5rem;
`;

const AvatarName = styled.h3`
    font-size: 1.1rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin: 0 0 0.5rem 0;
`;

const AvatarMeta = styled.div`
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
`;

const AvatarGender = styled.span`
    font-size: 0.9rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    background: ${(props) => props.theme.colors.apSectionBackground};
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
`;

const PremiumBadge = styled.span`
    font-size: 0.8rem;
    color: ${(props) => props.theme.colors.apYellow};
    background: rgba(255, 190, 43, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-weight: 500;
`;

const CustomAvatarSection = styled.div`
    background: #F8F8F8;
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    transition: all 0.3s ease;

    &:hover {
        cursor: pointer;
        background: linear-gradient(135deg, #f0f8ff, ${(props) => props.theme.colors.apSectionBackground});
    }
`;

const UploadIcon = styled.div`
    width: 80px;
    height: 80px;
    background: ${(props) => props.theme.colors.apButton};
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
`;

const UploadTitle = styled.h3`
    font-size: 1.5rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin-bottom: 1rem;
`;

const UploadDescription = styled.p`
    font-size: 1rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    margin-bottom: 2rem;
    line-height: 1.6;
`;

const ConfigurationPanel = styled.div`
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-top: 2rem;
		
`;

const FormGroup = styled.div`
    margin-bottom: 2rem;
`;

const Label = styled.label`
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin-bottom: 0.75rem;
`;

const Select = styled.select`
    width: 100%;
    padding: 1rem;
    border: 2px solid ${(props) => props.theme.colors.apLowMedGrey};
    border-radius: 12px;
    font-size: 1rem;
    background: white;
    color: ${(props) => props.theme.colors.apTextColor};
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: ${(props) => props.theme.colors.apButton};
        box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
    }
`;

const TextArea = styled.textarea`
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: 2px solid ${(props) => props.theme.colors.apLowMedGrey};
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    background: white;
    color: ${(props) => props.theme.colors.apTextColor};
    resize: vertical;
    transition: all 0.2s ease;

    &:focus {
        outline: none;
        border-color: ${(props) => props.theme.colors.apButton};
        box-shadow: 0 0 0 3px rgba(0, 51, 255, 0.1);
    }

    &::placeholder {
        color: ${(props) => props.theme.colors.apOffBlack};
    }
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;

    ${(props) => props.variant === 'secondary' ? `
        background: ${props.theme.colors.apThirdButton};
        color: ${props.theme.colors.apThirdButtonColor};
        border: 2px solid ${props.theme.colors.apLowMedGrey};

        &:hover {
            background: ${props.theme.colors.apThirdButtonHover};
            border-color: ${props.theme.colors.apButton};
						color: #ffffff;
        }
    ` : `
        background: ${props.theme.colors.apButton};
        color: white;

        &:hover {
            background: ${props.theme.colors.apButtonHover};
        }
    `}

    &:disabled {
        background: ${(props) => props.theme.colors.apGreyButton};
        color: ${(props) => props.theme.colors.disabledTextColor};
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
`;

const LoadingOverlay = styled.div`
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
`;

const LoadingContent = styled.div`
    background: white;
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    max-width: 400px;
    width: 90%;
    margin: auto;
`;

const LoadingTitle = styled.h3`
    font-size: 1.5rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin: 1rem 0 0.5rem;
`;

const LoadingDescription = styled.p`
    font-size: 1rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    margin: 0;
    line-height: 1.5;
`;

const ButtonGroup = styled.div`
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
`;

const PreviewImage = styled.img`
    width: 160px;
    height: 160px;
    object-fit: cover;
    border-radius: 12px;
    margin: 1rem auto;
    display: block;
    border: 2px solid ${(props) => props.theme.colors.apLowMedGrey};
`;

const SelectedAvatarDisplay = styled.div`
    display: flex;
    align-items: center;
    gap: 1rem;
    background: ${(props) => props.theme.colors.apSectionBackground};
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1rem;
    margin-bottom: 2rem;
`;

const SelectedAvatarImage = styled.img`
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
`;

const SelectedAvatarInfo = styled.div`
    flex: 1;
`;

const SelectedAvatarName = styled.h4`
    font-size: 1.1rem;
    font-weight: 600;
    color: ${(props) => props.theme.colors.apTextColor};
    margin: 0 0 0.25rem 0;
`;

const SelectedAvatarMeta = styled.p`
    font-size: 0.9rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    margin: 0;
`;

const CharacterCount = styled.div`
    font-size: 0.9rem;
    color: ${(props) => props.theme.colors.apOffBlack};
    text-align: right;
    margin-top: 0.5rem;
`;

const VoiceSelectionContainer = styled.div`
    display: flex;
    align-items: center;
    gap: 1rem;
`;

const VoiceSelectWrapper = styled.div`
    flex: 1;
`;

const AudioPreviewButton = styled.button<{ isPlaying?: boolean }>`
    background: ${(props) => props.isPlaying ? props.theme.colors.apButton : props.theme.colors.apSectionBackground};
    color: ${(props) => props.isPlaying ? 'white' : props.theme.colors.apTextColor};
    border: 2px solid ${(props) => props.isPlaying ? props.theme.colors.apButton : props.theme.colors.apLowMedGrey};
    border-radius: 8px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.2rem;

    &:hover {
        background: ${(props) => props.theme.colors.apButton};
        color: white;
        border-color: ${(props) => props.theme.colors.apButton};
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
`;

const VideoAvatarLibrary: React.FC<Props> = ({
    navigationUrl,
    saveChanges,
    setSaveChanges,
    showConfirmLeavingModal,
    setShowConfirmLeavingModal,
    hideCreateVideo
}) => {
    const translation = useTranslation();
    const { apiRetryHandler } = useTokenCheck();
    const navigate = useNavigate();
    const [myAvatars, setMyAvatars] = useState<MyHeyGenAvatar[]>([]);
    const [voices, setVoices] = useState<HeyGenVoice[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState("");
    const [selectedMyAvatar, setSelectedMyAvatar] = useState<MyHeyGenAvatar | null>(null);
    const [selectedVoice, setSelectedVoice] = useState<string>("");
    const [inputText, setInputText] = useState("");
    const [isGenerating, setIsGenerating] = useState(false);
    const [uploadedImage, setUploadedImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string>("");
    const [showConfiguration, setShowConfiguration] = useState(false);
    const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
    const [playingVoiceId, setPlayingVoiceId] = useState<string>("");
		const [displayFormat, setDisplayFormat] = useState(DisplayFormatOptions.PORTRAIT);

    // Image upload dropzone
    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        accept: {
            'image/*': ['.jpeg', '.jpg', '.png']
        },
        maxFiles: 1,
        onDrop: (acceptedFiles) => {
            if (acceptedFiles.length > 0) {
                resetAvatarSelect();
                const file = acceptedFiles[0];
                setUploadedImage(file);

                // Create preview
                const reader = new FileReader();
                reader.onload = () => {
                    setImagePreview(reader.result as string);
                };
                reader.readAsDataURL(file);

                setShowConfiguration(true);
            }
        }
    });

    // Fetch data on component mount
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                // Fetch avatars, my avatars, and voices in parallel
                const [myAvatarsResponse, voicesResponse] = await Promise.all([
                    apiRetryHandler(() => getMyHeyGenAvatars()),
                    apiRetryHandler(() => getHeyGenVoices())
                ]);

                if (myAvatarsResponse.error) {
                    console.warn("Failed to load my avatars:", myAvatarsResponse.error);
                } else if (myAvatarsResponse.data) {
                    setMyAvatars(myAvatarsResponse.data.avatar_list || []);
                }

                if (voicesResponse.error) {
                    console.warn("Failed to load voices:", voicesResponse.error);
                } else if (voicesResponse.data) {
                    setVoices(voicesResponse.data.voices || []);
                    // Set default voice
                    if (voicesResponse.data.voices.length > 0) {
                        setSelectedVoice(voicesResponse.data.voices[0].voice_id);
                    }
                }
            } catch (err) {
                setError("Failed to load data");
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    // Handle my avatar selection
    const handleMyAvatarSelect = (myAvatar: MyHeyGenAvatar) => {

      console.log(myAvatar);
        setSelectedMyAvatar(myAvatar);
        setShowConfiguration(true);
        setUploadedImage(null);
        setImagePreview("");
    };

    const resetAvatarSelect = () => {
        setSelectedMyAvatar(null);
        setShowConfiguration(true);
        setUploadedImage(null);
        setImagePreview("");
        setInputText("");
        setError("");
    };

    // Handle audio preview
    const handleAudioPreview = (voice: HeyGenVoice) => {
        if (!voice.preview_audio) return;

        // Stop current audio if playing
        if (currentAudio) {
            currentAudio.pause();
            currentAudio.currentTime = 0;
            setCurrentAudio(null);
            setPlayingVoiceId("");
        }

        // If clicking the same voice that's playing, just stop
        if (playingVoiceId === voice.voice_id) {
            return;
        }

        // Play new audio
        const audio = new Audio(voice.preview_audio);
        audio.addEventListener('ended', () => {
            setPlayingVoiceId("");
            setCurrentAudio(null);
        });

        audio.addEventListener('error', () => {
            setPlayingVoiceId("");
            setCurrentAudio(null);
            console.warn('Failed to play audio preview for voice:', voice.name);
        });

        setCurrentAudio(audio);
        setPlayingVoiceId(voice.voice_id);
        audio.play().catch(() => {
            setPlayingVoiceId("");
            setCurrentAudio(null);
        });
    };

    // Cleanup audio on unmount
    useEffect(() => {
        return () => {
            if (currentAudio) {
                currentAudio.pause();
                currentAudio.currentTime = 0;
            }
        };
    }, [currentAudio]);

    // Handle custom avatar creation using server-side API
    const handleCreateCustomAvatar = async () => {
        if (!uploadedImage) return;

        try {
            setIsGenerating(true);

            // Call server-side endpoint to create custom avatar
            const { data: createResponse, error: createError } = await apiRetryHandler(
                () => createCustomHeyGenAvatar(uploadedImage)
            );

            if (createError || !createResponse?.success) {
                setError("Failed to create custom avatar");
                return;
            }

            // Refresh My Avatars list to show the newly created avatar
            const refreshMyAvatars = async () => {
                try {
                    const myAvatarsResponse = await apiRetryHandler(() => getMyHeyGenAvatars());
                    if (myAvatarsResponse.data) {
                        resetAvatarSelect();
                        setMyAvatars(myAvatarsResponse.data.avatar_list || []);
                    }
                } catch (err) {
                    console.warn("Failed to refresh my avatars:", err);
                }
            };

            await refreshMyAvatars();
						setShowConfiguration(false);

        } catch (err) {
            console.error("Custom avatar creation error:", err);
            setError("Failed to create custom avatar: " + (err instanceof Error ? err.message : "Unknown error"));
        } finally {
            setIsGenerating(false);
        }
    };

    // Handle video generation and submission
    const handleGenerateVideo = async () => {
        if (!inputText.trim() || !selectedVoice || !selectedMyAvatar) {
            setError("Please enter text and select a voice and avatar");
            return;
        }

        try {
            setIsGenerating(true);
            setSaveChanges(true);

            const videoRequest = {
                title: "Custom Avatar Video",
                video_inputs: [
                    {
                        character: {
                        type: "talking_photo" as const,
                        talking_photo_id: selectedMyAvatar.id,
                        scale: 1.3,
                        talking_style: "stable" as const,
                        expression: "default" as const
                    	},
                        voice: {
                            type: "text" as const,
                            voice_id: selectedVoice,
                            input_text: inputText.trim(),
                            speed: 1.0
                        },
                        background: {
                            type: "color" as const,
                            value: "#f6f6fc"
                        }
                    }
                ],
                dimension: displayFormat === DisplayFormatOptions.PORTRAIT ? {
                    width: 1080,
                    height: 1920
                } : {
                    width: 1920,
                    height: 1080
                },
                caption: false
            };

            // Generate video
            const { data: videoData, error: videoError } = await apiRetryHandler(
                () => generateHeyGenVideo(videoRequest)
            );

            if (videoError || !videoData?.data?.data) {
                setError("Failed to generate video");
                return;
            }

            const videoId = videoData.data.data.video_id;

            // Poll for video completion
            await pollVideoStatus(videoId);
        } catch (err) {
            setError("Failed to generate video");
        }
    };

    // Poll video status and submit to API when ready
    const pollVideoStatus = async (videoId: string) => {
        const maxAttempts = 120; // 10 minutes with 5-second intervals
        let attempts = 0;

        const checkStatus = async () => {
           console.log("checkStatus -------");
            try {
                const { data: statusData, error: statusError } = await apiRetryHandler(
                    () => getHeyGenVideoStatus(videoId)
                );

                if (statusError || !statusData?.data?.data) {
                    setError("Failed to check video status");
                    return;
                }

                const { status, video_url } = statusData.data.data;

                if (status === "completed" && video_url) {
                    // Submit video URL to our API
                    const { error: uploadError } = await apiRetryHandler(
                        () => uploadVideoURL(video_url)
                    );

                    setIsGenerating(false);
                    setSaveChanges(false);

                    if (uploadError) {
                        setError("Failed to save video");
                    } else {
                        // Navigate to video library
                        navigate("/video-library");
                    }

                } else if (status === "waiting" || status === "processing") {
                    attempts++;
                    if (attempts < maxAttempts) {
                        setTimeout(checkStatus, 5000);
                    } else {
                        setError("Video generation timed out");
                    }
                }
            } catch (err) {
                setError("Failed to check video status");
            }
        };

        checkStatus();
    };

    if (loading) {
        return (
            <Container>
                <LoadingContent>
                    <Lottie
                        animationData={LoadingSpinnerAnimation}
                        style={{ width: 100, height: 100, margin: "auto" }}
                        loop={true}
                    />
                    <LoadingTitle>Loading Avatar Library</LoadingTitle>
                    <LoadingDescription>Please wait while we fetch the available avatars and voices...</LoadingDescription>
                </LoadingContent>
            </Container>
        );
    }

    return (
        <Container>
            {error && <ErrorMessage error={error} setError={setError} displayCloseIcon={true} />}

            {/* Custom Avatar Upload Section */}
						{myAvatars.length < 1 && (
            <Section>
                <SectionHeader>
                    <div>
                        <SectionTitle>Create Custom Avatar</SectionTitle>
                        <SectionDescription>Upload your photo to create a personalized talking avatar</SectionDescription>
                    </div>
                </SectionHeader>

                <CustomAvatarSection {...getRootProps()}>
                    <input {...getInputProps()} />
                    <UploadTitle>
                        {isDragActive ? "Drop your image here" : "Upload Your Photo"}
                    </UploadTitle>
                    <UploadDescription>
                        Drag and drop an image here, or click to select a file.<br />
                        Supported formats: JPG, PNG
                    </UploadDescription>
                    {imagePreview && (
                        <PreviewImage src={imagePreview} alt="Preview" />
                    )}
                </CustomAvatarSection>
            </Section>
						 )}

            {/* My Avatars Section */}
            {myAvatars.length > 0 && (
                <Section>
                    <SectionHeader>
                        <div>
                            <SectionTitle>My Avatars</SectionTitle>
                            <SectionDescription>Your custom created avatars</SectionDescription>
                        </div>
                    </SectionHeader>

                    <AvatarGrid>
															 
											<AvatarCard>
												{!imagePreview ? (
													<CustomAvatarSection {...getRootProps()}>
															<input {...getInputProps()} />
															<UploadTitle>
																	 Upload Your Photo
															</UploadTitle>
															<UploadDescription>
																	Drag and drop an image here, or click to select a file.<br />
																	Supported formats: JPG, PNG
															</UploadDescription>
													</CustomAvatarSection>
												) : (
													<>
															{imagePreview && (
																	<PreviewImage src={imagePreview} alt="Preview" />
															)}
															 
															<ActionButton
																style={{ padding: "5px", marginBottom: "10px" }}
																variant="secondary" 
																onClick={(e) => {
																	e.preventDefault();
																	resetAvatarSelect();
																	setShowConfiguration(false);
															}}>
																	Reset
															</ActionButton>
															
															<ActionButton 
																onClick={handleCreateCustomAvatar}
																disabled={isGenerating}
																style={{ padding: "5px" }}
															>
																	{isGenerating ? "Creating..." : "Create Avatar"}
															</ActionButton>
													</>
												)}
											</AvatarCard>
			 
                        {myAvatars.map((myAvatar) => (
                            <AvatarCard
                                key={myAvatar.id}
                                selected={selectedMyAvatar?.id === myAvatar.id}
                                onClick={() => handleMyAvatarSelect(myAvatar)}
																
                            >
                                <AvatarImage
                                    src={myAvatar.image_url}
                                    alt={myAvatar.name}
                                    onError={(e) => {
                                        (e.target as HTMLImageElement).src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk15IEF2YXRhcjwvdGV4dD48L3N2Zz4=";
                                    }}
                                />
                                {/* <AvatarInfo>
                                    <AvatarName>{myAvatar.name}</AvatarName>
                                    <AvatarMeta>
                                        <AvatarGender>{myAvatar.status}</AvatarGender>
                                        <PremiumBadge>Custom</PremiumBadge>
                                    </AvatarMeta>
                                </AvatarInfo> */}
                            </AvatarCard>
                        ))}
                    </AvatarGrid>
                </Section>
            )}

						{showConfiguration && myAvatars.length < 1  && (
                <ConfigurationPanel>
                    <ButtonGroup>
                        <ActionButton variant="secondary" onClick={(e) => {
                            e.preventDefault();
                            resetAvatarSelect();
                            setShowConfiguration(false);
                        }}>
                            Reset
                        </ActionButton>
                        {uploadedImage ? (
                            <ActionButton onClick={handleCreateCustomAvatar} disabled={isGenerating}>
                                {isGenerating ? "Creating..." : "Create Avatar"}
                            </ActionButton>
                        ) : (
                        <ActionButton
                            onClick={handleGenerateVideo}
                            disabled={isGenerating || !inputText.trim() || !selectedVoice || !selectedMyAvatar}
                        >
                            {isGenerating ? "Generating..." : "Generate Video"}
                        </ActionButton>
                        )}
                    </ButtonGroup>
                </ConfigurationPanel>
             )}

            {/* Configuration Panel */}
            {showConfiguration && selectedMyAvatar && (
                <ConfigurationPanel>
                     <SectionTitle style={{ textAlign: 'center' }}>Configure Your Video</SectionTitle>
                        {/* <SelectedAvatarDisplay>
                            <SelectedAvatarImage src={selectedMyAvatar.image_url} alt={selectedMyAvatar.name} />
                            <SelectedAvatarInfo>
                                <SelectedAvatarName>{selectedMyAvatar.name}</SelectedAvatarName>
                                <SelectedAvatarMeta>Custom Avatar • {selectedMyAvatar.status}</SelectedAvatarMeta>
                            </SelectedAvatarInfo>
                        </SelectedAvatarDisplay> */}
        
											<FormGroup>
													<Label>Select Voice</Label>
													<Select
															value={selectedVoice}
															onChange={(e) => setSelectedVoice(e.target.value)}
													>
															<option value="">Choose a voice...</option>
															{voices.map((voice) => (
																	<option key={voice.voice_id} value={voice.voice_id}>
																			{voice.name} ({voice.gender}, {voice.language})
																	</option>
															))}
													</Select>
													{selectedVoice && (
															<div style={{ marginTop: '1rem', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
																	<span style={{ fontSize: '0.9rem', color: '#666' }}>Listen:</span>
																	<AudioPreviewButton
																			onClick={() => {
																					const selectedVoiceData = voices.find(v => v.voice_id === selectedVoice);
																					if (selectedVoiceData) {
																							handleAudioPreview(selectedVoiceData);
																					}
																			}}
																			isPlaying={playingVoiceId === selectedVoice}
																			title="Listen voice"
																	>
																			{playingVoiceId === selectedVoice ? 'II' : '▶'}
																	</AudioPreviewButton>
															</div>
													)}
												<Label style={{ marginTop: '2rem' }}>Display Format</Label>
												<FlexSwitchRow>
													<FlexSwitchCol
														active={displayFormat === DisplayFormatOptions.PORTRAIT}
														onClick={() => setDisplayFormat(DisplayFormatOptions.PORTRAIT)}
													>
														<PortraitBox />
														{translation.editVideoPage.portrait}
													</FlexSwitchCol>
													<FlexSwitchCol
														active={displayFormat === DisplayFormatOptions.LANDSCAPE}
														onClick={() => setDisplayFormat(DisplayFormatOptions.LANDSCAPE)}
													>
														<LandscapeBox />
														{translation.editVideoPage.landscape}
													</FlexSwitchCol>
												</FlexSwitchRow>

													<Label style={{ marginTop: '1rem' }}>Enter Your Text</Label>
													<TextArea
															value={inputText}
															onChange={(e) => setInputText(e.target.value)}
															placeholder="Enter the text you want your avatar to speak..."
															rows={4}
													/>
											</FormGroup>
        

                    {/* Action Buttons */}
                    <ButtonGroup>
                        <ActionButton variant="secondary" onClick={(e) => {
                            e.preventDefault();
                            resetAvatarSelect();
                            setShowConfiguration(false);
                        }}>
                            Reset
                        </ActionButton>
           
                        <ActionButton
                            onClick={handleGenerateVideo}
                            disabled={isGenerating || !inputText.trim() || !selectedVoice || !selectedMyAvatar}
                        >
                            {isGenerating ? "Generating..." : "Generate Video"}
                        </ActionButton>
     
                    </ButtonGroup>
                </ConfigurationPanel>
            	)}
							{isGenerating && (
								<LoadingOverlay>
										<Lottie
												animationData={LoadingSpinnerAnimation}
												style={{ width: 80, height: 80 }}
												loop={true}
										/>
										<LoadingTitle style={{ color: '#ffffff' }}>
												Creating Custom Avatar...
										</LoadingTitle>
										<LoadingDescription style={{ color: '#ffffff' }}>
												Please wait while we create your custom avatar. This may take several minutes.
										</LoadingDescription>
								</LoadingOverlay>
							)}
        </Container>
    );
};

export default VideoAvatarLibrary;
